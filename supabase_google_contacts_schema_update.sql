-- Supabase Schema Update for Google Contacts Import
-- Enhances customers table to support Google Contacts data with Square Customer IDs
-- Run this in Supabase SQL Editor before importing Google Contacts data

-- Step 1: Add new columns to customers table for Google Contacts data
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS square_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS acquisition_source VARCHAR(100),
ADD COLUMN IF NOT EXISTS transaction_count NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_spend NUMERIC(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS first_visit TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_square_visit TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS email_subscription_status VARCHAR(50) DEFAULT 'unknown',
ADD COLUMN IF NOT EXISTS sms_subscription_status VARCHAR(50) DEFAULT 'unknown',
ADD COLUMN IF NOT EXISTS customer_memo TEXT,
ADD COLUMN IF NOT EXISTS import_source VARCHAR(100) DEFAULT 'manual',
ADD COLUMN IF NOT EXISTS import_date TIMESTAMPTZ DEFAULT NOW();

-- Step 2: Create index on square_customer_id for payment integration performance
CREATE INDEX IF NOT EXISTS idx_customers_square_customer_id 
ON public.customers(square_customer_id) 
WHERE square_customer_id IS NOT NULL;

-- Step 3: Create index on acquisition_source for analytics
CREATE INDEX IF NOT EXISTS idx_customers_acquisition_source 
ON public.customers(acquisition_source) 
WHERE acquisition_source IS NOT NULL;

-- Step 4: Create index on email for duplicate detection during import
CREATE INDEX IF NOT EXISTS idx_customers_email_lower 
ON public.customers(LOWER(email));

-- Step 5: Add constraint to ensure Square Customer IDs are unique when present
ALTER TABLE public.customers 
ADD CONSTRAINT unique_square_customer_id 
UNIQUE (square_customer_id);

-- Step 6: Create customer import log table for tracking imports
CREATE TABLE IF NOT EXISTS public.customer_import_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  import_batch_id UUID NOT NULL,
  filename VARCHAR(255),
  total_records INTEGER DEFAULT 0,
  successful_imports INTEGER DEFAULT 0,
  failed_imports INTEGER DEFAULT 0,
  duplicate_emails INTEGER DEFAULT 0,
  import_status VARCHAR(50) DEFAULT 'in_progress',
  error_details JSONB,
  imported_by UUID, -- References auth.users
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 7: Create customer import errors table for detailed error tracking
CREATE TABLE IF NOT EXISTS public.customer_import_errors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  import_batch_id UUID NOT NULL,
  row_number INTEGER,
  customer_email VARCHAR(255),
  customer_name VARCHAR(255),
  error_type VARCHAR(100),
  error_message TEXT,
  raw_data JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 8: Create function to update customer analytics
CREATE OR REPLACE FUNCTION update_customer_analytics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the updated_at timestamp
  NEW.updated_at = NOW();
  
  -- If transaction_count or total_spend changed, log the update
  IF OLD.transaction_count IS DISTINCT FROM NEW.transaction_count 
     OR OLD.total_spend IS DISTINCT FROM NEW.total_spend THEN
    -- Could add logging here if needed
    NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 9: Create trigger for customer analytics updates
DROP TRIGGER IF EXISTS trigger_update_customer_analytics ON public.customers;
CREATE TRIGGER trigger_update_customer_analytics
  BEFORE UPDATE ON public.customers
  FOR EACH ROW
  EXECUTE FUNCTION update_customer_analytics();

-- Step 10: Create view for customer analytics dashboard
CREATE OR REPLACE VIEW public.customer_analytics_view AS
SELECT 
  c.id,
  c.name,
  c.email,
  c.phone,
  c.square_customer_id,
  c.acquisition_source,
  c.transaction_count,
  c.total_spend,
  c.first_visit,
  c.last_square_visit,
  c.marketing_consent,
  c.email_subscription_status,
  c.sms_subscription_status,
  c.created_at,
  c.import_source,
  c.import_date,
  -- Calculate customer metrics
  CASE 
    WHEN c.total_spend >= 500 THEN 'VIP'
    WHEN c.total_spend >= 200 THEN 'Premium'
    WHEN c.total_spend >= 50 THEN 'Regular'
    ELSE 'New'
  END as customer_tier,
  CASE 
    WHEN c.last_square_visit IS NULL THEN 'Never Visited'
    WHEN c.last_square_visit > NOW() - INTERVAL '30 days' THEN 'Recent'
    WHEN c.last_square_visit > NOW() - INTERVAL '90 days' THEN 'Occasional'
    ELSE 'Inactive'
  END as visit_status,
  -- Calculate days since last visit
  CASE 
    WHEN c.last_square_visit IS NOT NULL 
    THEN EXTRACT(days FROM NOW() - c.last_square_visit)::INTEGER
    ELSE NULL
  END as days_since_last_visit,
  -- Calculate customer lifetime (days since first visit)
  CASE 
    WHEN c.first_visit IS NOT NULL 
    THEN EXTRACT(days FROM NOW() - c.first_visit)::INTEGER
    ELSE NULL
  END as customer_lifetime_days
FROM public.customers c
WHERE c.email IS NOT NULL;

-- Step 11: Create function for safe customer import with duplicate handling
CREATE OR REPLACE FUNCTION import_customer_with_duplicate_check(
  p_name TEXT,
  p_email TEXT,
  p_phone TEXT DEFAULT NULL,
  p_address TEXT DEFAULT NULL,
  p_city TEXT DEFAULT NULL,
  p_state TEXT DEFAULT NULL,
  p_postal_code TEXT DEFAULT NULL,
  p_country TEXT DEFAULT 'Australia',
  p_notes TEXT DEFAULT NULL,
  p_marketing_consent BOOLEAN DEFAULT FALSE,
  p_square_customer_id VARCHAR(255) DEFAULT NULL,
  p_acquisition_source VARCHAR(100) DEFAULT NULL,
  p_transaction_count NUMERIC DEFAULT 0,
  p_total_spend NUMERIC DEFAULT 0.00,
  p_first_visit TIMESTAMPTZ DEFAULT NULL,
  p_last_square_visit TIMESTAMPTZ DEFAULT NULL,
  p_email_subscription_status VARCHAR(50) DEFAULT 'unknown',
  p_sms_subscription_status VARCHAR(50) DEFAULT 'unknown',
  p_customer_memo TEXT DEFAULT NULL,
  p_import_source VARCHAR(100) DEFAULT 'google_contacts'
)
RETURNS JSONB AS $$
DECLARE
  existing_customer_id UUID;
  new_customer_id UUID;
  result JSONB;
BEGIN
  -- Check for existing customer by email (case-insensitive)
  SELECT id INTO existing_customer_id 
  FROM public.customers 
  WHERE LOWER(email) = LOWER(p_email);
  
  IF existing_customer_id IS NOT NULL THEN
    -- Customer exists, return duplicate info
    result := jsonb_build_object(
      'status', 'duplicate',
      'customer_id', existing_customer_id,
      'message', 'Customer with this email already exists'
    );
  ELSE
    -- Insert new customer
    INSERT INTO public.customers (
      name, email, phone, address, city, state, postal_code, country,
      notes, marketing_consent, square_customer_id, acquisition_source,
      transaction_count, total_spend, first_visit, last_square_visit,
      email_subscription_status, sms_subscription_status, customer_memo,
      import_source, import_date
    ) VALUES (
      p_name, p_email, p_phone, p_address, p_city, p_state, p_postal_code, p_country,
      p_notes, p_marketing_consent, p_square_customer_id, p_acquisition_source,
      p_transaction_count, p_total_spend, p_first_visit, p_last_square_visit,
      p_email_subscription_status, p_sms_subscription_status, p_customer_memo,
      p_import_source, NOW()
    ) RETURNING id INTO new_customer_id;
    
    result := jsonb_build_object(
      'status', 'success',
      'customer_id', new_customer_id,
      'message', 'Customer imported successfully'
    );
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Step 12: Grant necessary permissions (adjust as needed for your setup)
-- Note: These permissions may need to be adjusted based on your Supabase RLS policies

-- Grant access to import tables for admin users
GRANT ALL ON public.customer_import_logs TO authenticated;
GRANT ALL ON public.customer_import_errors TO authenticated;
GRANT SELECT ON public.customer_analytics_view TO authenticated;

-- Step 13: Create RLS policies for import tables (if RLS is enabled)
-- Uncomment and adjust these if you have Row Level Security enabled

/*
ALTER TABLE public.customer_import_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_import_errors ENABLE ROW LEVEL SECURITY;

-- Policy for admin users to access import logs
CREATE POLICY "Admin users can access import logs" ON public.customer_import_logs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' IN ('dev', 'admin')
    )
  );

-- Policy for admin users to access import errors
CREATE POLICY "Admin users can access import errors" ON public.customer_import_errors
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' IN ('dev', 'admin')
    )
  );
*/

-- Step 14: Insert sample acquisition sources for reference
INSERT INTO public.customer_import_logs (import_batch_id, filename, import_status, total_records)
VALUES (uuid_generate_v4(), 'schema_update_initialization', 'completed', 0)
ON CONFLICT DO NOTHING;

-- Verification queries to check the schema updates
-- Run these to verify the changes were applied correctly:

-- Check new columns exist
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'customers' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check indexes were created
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'customers' 
AND schemaname = 'public';

-- Check new tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('customer_import_logs', 'customer_import_errors');

-- Success message
SELECT 'Schema update completed successfully! Ready for Google Contacts import.' as status;
