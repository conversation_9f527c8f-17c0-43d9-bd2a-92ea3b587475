.customersImport {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.titleSection h1 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 32px;
  font-weight: 700;
}

.subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
  line-height: 1.5;
}

.headerActions {
  display: flex;
  gap: 12px;
}

.backButton {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.backButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  color: #dc2626;
  font-size: 14px;
}

.statsSection {
  margin-bottom: 32px;
}

.statsSection h2 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.statCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.statNumber {
  font-size: 36px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  display: block;
}

.statLabel {
  font-size: 14px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.importSection {
  margin-bottom: 40px;
}

.historySection {
  margin-bottom: 40px;
}

.historySection h2 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #6b7280;
  font-size: 16px;
}

.emptyState {
  text-align: center;
  padding: 40px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
}

.historyTable {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.historyTable table {
  width: 100%;
  border-collapse: collapse;
}

.historyTable th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 1px solid #e5e7eb;
}

.historyTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
  color: #1f2937;
}

.historyTable tr:last-child td {
  border-bottom: none;
}

.historyTable tr:hover {
  background: #f9fafb;
}

.filename {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #4b5563;
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusCompleted {
  background: #d1fae5;
  color: #065f46;
}

.statusWarning {
  background: #fef3c7;
  color: #92400e;
}

.statusProgress {
  background: #dbeafe;
  color: #1e40af;
}

.statusFailed {
  background: #fee2e2;
  color: #991b1b;
}

.successCount {
  color: #059669;
  font-weight: 600;
}

.duplicateCount {
  color: #d97706;
  font-weight: 600;
}

.errorCount {
  color: #dc2626;
  font-weight: 600;
}

.viewButton {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.viewButton:hover {
  background: #2563eb;
}

.guidelinesSection {
  margin-bottom: 40px;
}

.guidelinesSection h2 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.guidelines {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.guidelineCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.guidelineCard h3 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.guidelineCard ul {
  margin: 0;
  padding-left: 20px;
  color: #4b5563;
}

.guidelineCard li {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.4;
}

.actionsSection {
  margin-bottom: 40px;
}

.actionsSection h2 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.actionButtons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.actionButton {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.actionButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .customersImport {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .guidelines {
    grid-template-columns: 1fr;
  }
  
  .actionButtons {
    flex-direction: column;
  }
  
  .historyTable {
    overflow-x: auto;
  }
  
  .historyTable table {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .statCard {
    padding: 16px;
  }
  
  .statNumber {
    font-size: 28px;
  }
}
