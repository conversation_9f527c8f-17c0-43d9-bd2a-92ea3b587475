.customerImport {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.header {
  margin-bottom: 24px;
  text-align: center;
}

.header h3 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 24px;
  font-weight: 600;
}

.description {
  color: #666;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.uploadSection {
  margin-bottom: 24px;
}

.dropZone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 48px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafafa;
}

.dropZone:hover,
.dropZone.dragActive {
  border-color: #3b82f6;
  background: #eff6ff;
}

.dropZoneContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.uploadIcon {
  font-size: 48px;
  opacity: 0.6;
}

.dropZoneText {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.fileRequirements {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.hiddenFileInput {
  display: none;
}

.actions {
  margin-top: 16px;
  text-align: center;
}

.sampleButton {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sampleButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.uploadProgress {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progressHeader h4 {
  margin: 0;
  color: #1e293b;
  font-size: 18px;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
}

.progressText {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  text-align: center;
}

.importResults {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
}

.resultsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.resultsHeader h4 {
  margin: 0;
  color: #166534;
  font-size: 18px;
}

.newImportButton {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.newImportButton:hover {
  background: #2563eb;
}

.resultsSummary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.statCard {
  background: white;
  border: 1px solid #d1fae5;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
}

.statNumber {
  font-size: 24px;
  font-weight: 700;
  color: #059669;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.errorSection {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.errorSection h5 {
  margin: 0 0 16px 0;
  color: #dc2626;
  font-size: 16px;
}

.errorList {
  max-height: 200px;
  overflow-y: auto;
}

.errorItem {
  display: flex;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #fee2e2;
  font-size: 14px;
}

.errorItem:last-child {
  border-bottom: none;
}

.errorRow {
  font-weight: 600;
  color: #991b1b;
  min-width: 60px;
}

.errorMessage {
  color: #7f1d1d;
  flex: 1;
}

.moreErrors {
  margin: 12px 0 0 0;
  font-size: 14px;
  color: #991b1b;
  font-style: italic;
}

.retryButton {
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 16px;
  transition: background 0.2s ease;
}

.retryButton:hover {
  background: #b91c1c;
}

.importGuide {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.importGuide h5 {
  margin: 0 0 12px 0;
  color: #1e293b;
  font-size: 16px;
}

.guideList {
  margin: 0;
  padding-left: 20px;
  color: #475569;
}

.guideList li {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .customerImport {
    padding: 16px;
    margin: 0 16px;
  }
  
  .dropZone {
    padding: 32px 16px;
  }
  
  .resultsSummary {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .resultsHeader {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .errorItem {
    flex-direction: column;
    gap: 4px;
  }
  
  .errorRow {
    min-width: auto;
  }
}

/* Animation for progress bar */
@keyframes progressPulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.progressFill {
  animation: progressPulse 2s ease-in-out infinite;
}

/* Success state styling */
.importResults .statCard:first-child .statNumber {
  color: #1f2937;
}

.importResults .statCard:nth-child(2) .statNumber {
  color: #059669;
}

.importResults .statCard:nth-child(3) .statNumber {
  color: #d97706;
}

.importResults .statCard:nth-child(4) .statNumber {
  color: #dc2626;
}
