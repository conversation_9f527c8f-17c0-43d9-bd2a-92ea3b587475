import supabase, { getCurrentUserFromRequest } from '@/lib/supabase';

/**
 * Customer Import Status API Endpoint
 * Provides real-time status updates for customer import operations
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check authentication and admin permissions
    const { user, role } = await getCurrentUserFromRequest(req);
    const allowedRoles = ['dev', 'admin'];
    if (!user || !allowedRoles.includes(role)) {
      return res.status(401).json({ error: 'Unauthorized. Admin access required.' });
    }

    const { batchId } = req.query;
    
    if (!batchId) {
      return res.status(400).json({ error: 'Batch ID is required' });
    }

    // Get import log details
    const { data: importLog, error: logError } = await supabase
      .from('customer_import_logs')
      .select('*')
      .eq('import_batch_id', batchId)
      .single();

    if (logError) {
      console.error('Error fetching import log:', logError);
      return res.status(404).json({ error: 'Import batch not found' });
    }

    // Get import errors if any
    const { data: importErrors, error: errorsError } = await supabase
      .from('customer_import_errors')
      .select('*')
      .eq('import_batch_id', batchId)
      .order('row_number', { ascending: true });

    if (errorsError) {
      console.error('Error fetching import errors:', errorsError);
    }

    // Calculate progress percentage
    const progressPercentage = importLog.total_records > 0 
      ? Math.round(((importLog.successful_imports + importLog.failed_imports + importLog.duplicate_emails) / importLog.total_records) * 100)
      : 0;

    // Determine overall status
    let overallStatus = importLog.import_status;
    if (overallStatus === 'in_progress' && progressPercentage === 100) {
      overallStatus = importLog.failed_imports > 0 ? 'completed_with_errors' : 'completed';
    }

    return res.status(200).json({
      batchId,
      status: overallStatus,
      progress: {
        percentage: progressPercentage,
        totalRecords: importLog.total_records || 0,
        successfulImports: importLog.successful_imports || 0,
        failedImports: importLog.failed_imports || 0,
        duplicateEmails: importLog.duplicate_emails || 0
      },
      details: {
        filename: importLog.filename,
        startedAt: importLog.started_at,
        completedAt: importLog.completed_at,
        importedBy: importLog.imported_by
      },
      errors: importErrors || [],
      summary: {
        hasErrors: (importLog.failed_imports || 0) > 0,
        hasDuplicates: (importLog.duplicate_emails || 0) > 0,
        isComplete: overallStatus !== 'in_progress',
        successRate: importLog.total_records > 0 
          ? Math.round((importLog.successful_imports / importLog.total_records) * 100)
          : 0
      }
    });

  } catch (error) {
    console.error('Import status error:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch import status', 
      details: error.message 
    });
  }
}
