import supabase, { getCurrentUserFromRequest } from '@/lib/supabase';

/**
 * Customer Import History API Endpoint
 * Provides import history and statistics for admin dashboard
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check authentication and admin permissions
    const { user, role } = await getCurrentUserFromRequest(req);
    const allowedRoles = ['dev', 'admin'];
    if (!user || !allowedRoles.includes(role)) {
      return res.status(401).json({ error: 'Unauthorized. Admin access required.' });
    }

    const { limit = 20, offset = 0 } = req.query;

    // Get import history with pagination
    const { data: imports, error: importsError } = await supabase
      .from('customer_import_logs')
      .select('*')
      .order('started_at', { ascending: false })
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    if (importsError) {
      console.error('Error fetching import history:', importsError);
      return res.status(500).json({ error: 'Failed to fetch import history' });
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('customer_import_logs')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error fetching import count:', countError);
    }

    return res.status(200).json({
      imports: imports || [],
      pagination: {
        total: count || 0,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < (count || 0)
      }
    });

  } catch (error) {
    console.error('Import history error:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch import history', 
      details: error.message 
    });
  }
}
