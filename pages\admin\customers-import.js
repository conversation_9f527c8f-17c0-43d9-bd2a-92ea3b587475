import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import CustomerImport from '@/components/admin/CustomerImport';
import { authenticatedFetch } from '@/lib/auth-utils';
import styles from '@/styles/admin/CustomersImport.module.css';

/**
 * Enhanced Customer Import Management Page
 * Provides interface for importing Google Contacts CSV data with Square Customer IDs
 */
export default function CustomersImport() {
  const router = useRouter();
  const [importHistory, setImportHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalCustomers: 0,
    recentImports: 0,
    squareCustomers: 0,
    marketingOptIns: 0
  });

  useEffect(() => {
    fetchImportHistory();
    fetchCustomerStats();
  }, []);

  const fetchImportHistory = async () => {
    try {
      const response = await authenticatedFetch('/api/admin/customers/import-history');
      if (response.ok) {
        const data = await response.json();
        setImportHistory(data.imports || []);
      }
    } catch (error) {
      console.error('Error fetching import history:', error);
      setError('Failed to load import history');
    }
  };

  const fetchCustomerStats = async () => {
    try {
      const response = await authenticatedFetch('/api/admin/customers/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching customer stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleImportComplete = (result) => {
    // Refresh import history and stats
    fetchImportHistory();
    fetchCustomerStats();
    
    // Show success message
    console.log('Import completed:', result);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      'completed': styles.statusCompleted,
      'completed_with_errors': styles.statusWarning,
      'in_progress': styles.statusProgress,
      'failed': styles.statusFailed
    };

    return (
      <span className={`${styles.statusBadge} ${statusClasses[status] || ''}`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  return (
    <AdminLayout>
      <div className={styles.customersImport}>
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <h1>Customer Import Management</h1>
            <p className={styles.subtitle}>
              Import customer data from Google Contacts with Square Customer ID integration
            </p>
          </div>
          
          <div className={styles.headerActions}>
            <button
              onClick={() => router.push('/admin/customers')}
              className={styles.backButton}
            >
              ← Back to Customers
            </button>
          </div>
        </div>

        {error && <div className={styles.error}>{error}</div>}

        {/* Customer Statistics */}
        <div className={styles.statsSection}>
          <h2>Customer Database Overview</h2>
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>{stats.totalCustomers}</div>
              <div className={styles.statLabel}>Total Customers</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>{stats.squareCustomers}</div>
              <div className={styles.statLabel}>Square Customers</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>{stats.marketingOptIns}</div>
              <div className={styles.statLabel}>Marketing Opt-ins</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>{stats.recentImports}</div>
              <div className={styles.statLabel}>Recent Imports</div>
            </div>
          </div>
        </div>

        {/* Import Interface */}
        <div className={styles.importSection}>
          <CustomerImport onImportComplete={handleImportComplete} />
        </div>

        {/* Import History */}
        <div className={styles.historySection}>
          <h2>Import History</h2>
          
          {loading ? (
            <div className={styles.loading}>Loading import history...</div>
          ) : importHistory.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No imports found. Upload your first CSV file to get started.</p>
            </div>
          ) : (
            <div className={styles.historyTable}>
              <table>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Filename</th>
                    <th>Status</th>
                    <th>Records</th>
                    <th>Success</th>
                    <th>Duplicates</th>
                    <th>Errors</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {importHistory.map((importRecord) => (
                    <tr key={importRecord.import_batch_id}>
                      <td>{formatDate(importRecord.started_at)}</td>
                      <td className={styles.filename}>{importRecord.filename}</td>
                      <td>{getStatusBadge(importRecord.import_status)}</td>
                      <td>{importRecord.total_records || 0}</td>
                      <td className={styles.successCount}>
                        {importRecord.successful_imports || 0}
                      </td>
                      <td className={styles.duplicateCount}>
                        {importRecord.duplicate_emails || 0}
                      </td>
                      <td className={styles.errorCount}>
                        {importRecord.failed_imports || 0}
                      </td>
                      <td>
                        <button
                          onClick={() => router.push(`/admin/customers/import-details/${importRecord.import_batch_id}`)}
                          className={styles.viewButton}
                        >
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Import Guidelines */}
        <div className={styles.guidelinesSection}>
          <h2>Import Guidelines</h2>
          <div className={styles.guidelines}>
            <div className={styles.guidelineCard}>
              <h3>📁 File Format</h3>
              <ul>
                <li>CSV files only (max 10MB)</li>
                <li>UTF-8 encoding recommended</li>
                <li>Headers must match expected format</li>
              </ul>
            </div>
            
            <div className={styles.guidelineCard}>
              <h3>💳 Square Integration</h3>
              <ul>
                <li>Square Customer IDs will be preserved</li>
                <li>Transaction history stored in notes</li>
                <li>Payment integration maintained</li>
              </ul>
            </div>
            
            <div className={styles.guidelineCard}>
              <h3>📧 Marketing Data</h3>
              <ul>
                <li>Email subscription status imported</li>
                <li>Marketing consent flags set automatically</li>
                <li>SMS preferences preserved</li>
              </ul>
            </div>
            
            <div className={styles.guidelineCard}>
              <h3>🔄 Duplicate Handling</h3>
              <ul>
                <li>Duplicates detected by email address</li>
                <li>Existing customers will not be overwritten</li>
                <li>Duplicate report provided after import</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className={styles.actionsSection}>
          <h2>Quick Actions</h2>
          <div className={styles.actionButtons}>
            <button
              onClick={() => router.push('/admin/customers')}
              className={styles.actionButton}
            >
              View All Customers
            </button>
            <button
              onClick={() => router.push('/admin/customers/export')}
              className={styles.actionButton}
            >
              Export Customer Data
            </button>
            <button
              onClick={() => router.push('/admin/customers/analytics')}
              className={styles.actionButton}
            >
              Customer Analytics
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
