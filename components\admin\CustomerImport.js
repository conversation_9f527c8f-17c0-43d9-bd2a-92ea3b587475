import { useState, useRef } from 'react';
import { authenticatedFetch } from '@/lib/auth-utils';
import styles from '@/styles/admin/CustomerImport.module.css';

/**
 * Enhanced Customer Import Component
 * Handles CSV file uploads with validation, progress tracking, and error reporting
 * Specifically designed for Google Contacts import data
 */
export default function CustomerImport({ onImportComplete }) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [importStatus, setImportStatus] = useState(null);
  const [importResults, setImportResults] = useState(null);
  const [errors, setErrors] = useState([]);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  const handleFileSelect = (file) => {
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setErrors(['Only CSV files are supported']);
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setErrors(['File size must be less than 10MB']);
      return;
    }

    uploadFile(file);
  };

  const uploadFile = async (file) => {
    setIsUploading(true);
    setUploadProgress(0);
    setErrors([]);
    setImportStatus('uploading');

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await authenticatedFetch('/api/admin/customers/import', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      setImportResults(result.results);
      setImportStatus('completed');

      // Track import progress if needed
      if (result.importBatchId) {
        trackImportProgress(result.importBatchId);
      }

      if (onImportComplete) {
        onImportComplete(result);
      }

    } catch (error) {
      console.error('Upload error:', error);
      setErrors([error.message]);
      setImportStatus('error');
    } finally {
      setIsUploading(false);
      setUploadProgress(100);
    }
  };

  const trackImportProgress = async (batchId) => {
    try {
      const response = await authenticatedFetch(`/api/admin/customers/import-status/${batchId}`);
      if (response.ok) {
        const status = await response.json();
        setImportResults(status);
      }
    } catch (error) {
      console.error('Error tracking import progress:', error);
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const resetImport = () => {
    setImportStatus(null);
    setImportResults(null);
    setErrors([]);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const downloadSampleCSV = () => {
    const sampleData = [
      'name,email,phone,address,city,state,postal_code,country,notes,marketing_consent,square_customer_id,acquisition_source,transaction_count,total_spend,first_visit,last_square_visit',
      'John Doe,<EMAIL>,+***********,123 Main St,Melbourne,VIC,3000,Australia,Sample customer notes,true,SQUARE123,google_contacts,2,150.00,2023-01-15,2023-12-01',
      'Jane Smith,<EMAIL>,+***********,456 Queen St,Sydney,NSW,2000,Australia,Another sample customer,false,SQUARE456,appointments,1,75.50,2023-06-10,2023-11-15'
    ].join('\n');

    const blob = new Blob([sampleData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'customer_import_sample.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className={styles.customerImport}>
      <div className={styles.header}>
        <h3>Import Customers</h3>
        <p className={styles.description}>
          Upload a CSV file to import customer data. Supports Google Contacts export format with Square Customer IDs.
        </p>
      </div>

      {!importStatus && (
        <div className={styles.uploadSection}>
          <div
            className={`${styles.dropZone} ${dragActive ? styles.dragActive : ''}`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <div className={styles.dropZoneContent}>
              <div className={styles.uploadIcon}>📁</div>
              <p className={styles.dropZoneText}>
                Drag and drop your CSV file here, or click to browse
              </p>
              <p className={styles.fileRequirements}>
                Supports CSV files up to 10MB
              </p>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={handleFileInputChange}
            className={styles.hiddenFileInput}
          />

          <div className={styles.actions}>
            <button
              type="button"
              onClick={downloadSampleCSV}
              className={styles.sampleButton}
            >
              Download Sample CSV
            </button>
          </div>
        </div>
      )}

      {isUploading && (
        <div className={styles.uploadProgress}>
          <div className={styles.progressHeader}>
            <h4>Uploading and Processing...</h4>
            <span>{uploadProgress}%</span>
          </div>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
          <p className={styles.progressText}>
            Please wait while we process your customer data...
          </p>
        </div>
      )}

      {importStatus === 'completed' && importResults && (
        <div className={styles.importResults}>
          <div className={styles.resultsHeader}>
            <h4>Import Complete</h4>
            <button
              onClick={resetImport}
              className={styles.newImportButton}
            >
              Import Another File
            </button>
          </div>

          <div className={styles.resultsSummary}>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>{importResults.totalRecords}</div>
              <div className={styles.statLabel}>Total Records</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>{importResults.successfulImports}</div>
              <div className={styles.statLabel}>Successful</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>{importResults.duplicateEmails}</div>
              <div className={styles.statLabel}>Duplicates</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>{importResults.failedImports}</div>
              <div className={styles.statLabel}>Failed</div>
            </div>
          </div>

          {importResults.errors && importResults.errors.length > 0 && (
            <div className={styles.errorSection}>
              <h5>Import Errors</h5>
              <div className={styles.errorList}>
                {importResults.errors.slice(0, 10).map((error, index) => (
                  <div key={index} className={styles.errorItem}>
                    <span className={styles.errorRow}>Row {error.rowNumber}:</span>
                    <span className={styles.errorMessage}>{error.error}</span>
                  </div>
                ))}
                {importResults.errors.length > 10 && (
                  <p className={styles.moreErrors}>
                    ... and {importResults.errors.length - 10} more errors
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {errors.length > 0 && (
        <div className={styles.errorSection}>
          <h5>Upload Errors</h5>
          <div className={styles.errorList}>
            {errors.map((error, index) => (
              <div key={index} className={styles.errorItem}>
                <span className={styles.errorMessage}>{error}</span>
              </div>
            ))}
          </div>
          <button
            onClick={resetImport}
            className={styles.retryButton}
          >
            Try Again
          </button>
        </div>
      )}

      <div className={styles.importGuide}>
        <h5>Import Guidelines</h5>
        <ul className={styles.guideList}>
          <li>CSV file must include headers: name, email (required)</li>
          <li>Supports Google Contacts export format</li>
          <li>Square Customer IDs will be preserved for payment integration</li>
          <li>Duplicate emails will be skipped automatically</li>
          <li>Phone numbers will be standardized to Australian format</li>
          <li>Marketing consent will be imported from subscription status</li>
        </ul>
      </div>
    </div>
  );
}
