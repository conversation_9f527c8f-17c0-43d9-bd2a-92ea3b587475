import supabase, { getCurrentUserFromRequest } from '@/lib/supabase';

/**
 * Customer Statistics API Endpoint
 * Provides customer database statistics for admin dashboard
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check authentication and admin permissions
    const { user, role } = await getCurrentUserFromRequest(req);
    const allowedRoles = ['dev', 'admin', 'artist', 'braider'];
    if (!user || !allowedRoles.includes(role)) {
      return res.status(401).json({ error: 'Unauthorized. Admin access required.' });
    }

    // Get total customers count
    const { count: totalCustomers, error: totalError } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true });

    if (totalError) {
      console.error('Error fetching total customers:', totalError);
    }

    // Get customers with Square Customer IDs
    const { count: squareCustomers, error: squareError } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true })
      .not('square_customer_id', 'is', null);

    if (squareError) {
      console.error('Error fetching Square customers:', squareError);
    }

    // Get customers with marketing consent
    const { count: marketingOptIns, error: marketingError } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true })
      .eq('marketing_consent', true);

    if (marketingError) {
      console.error('Error fetching marketing opt-ins:', marketingError);
    }

    // Get recent imports (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { count: recentImports, error: importsError } = await supabase
      .from('customer_import_logs')
      .select('*', { count: 'exact', head: true })
      .gte('started_at', thirtyDaysAgo.toISOString());

    if (importsError) {
      console.error('Error fetching recent imports:', importsError);
    }

    // Get acquisition source breakdown
    const { data: acquisitionSources, error: acquisitionError } = await supabase
      .from('customers')
      .select('acquisition_source')
      .not('acquisition_source', 'is', null);

    if (acquisitionError) {
      console.error('Error fetching acquisition sources:', acquisitionError);
    }

    // Process acquisition source data
    const acquisitionBreakdown = {};
    if (acquisitionSources) {
      acquisitionSources.forEach(customer => {
        const source = customer.acquisition_source || 'unknown';
        acquisitionBreakdown[source] = (acquisitionBreakdown[source] || 0) + 1;
      });
    }

    // Get customer growth data (last 12 months)
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const { data: growthData, error: growthError } = await supabase
      .from('customers')
      .select('created_at')
      .gte('created_at', twelveMonthsAgo.toISOString())
      .order('created_at', { ascending: true });

    if (growthError) {
      console.error('Error fetching growth data:', growthError);
    }

    // Process growth data by month
    const monthlyGrowth = {};
    if (growthData) {
      growthData.forEach(customer => {
        const date = new Date(customer.created_at);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        monthlyGrowth[monthKey] = (monthlyGrowth[monthKey] || 0) + 1;
      });
    }

    // Get subscription status breakdown
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from('customers')
      .select('email_subscription_status, sms_subscription_status');

    if (subscriptionError) {
      console.error('Error fetching subscription data:', subscriptionError);
    }

    // Process subscription data
    const emailSubscriptions = {};
    const smsSubscriptions = {};
    if (subscriptionData) {
      subscriptionData.forEach(customer => {
        const emailStatus = customer.email_subscription_status || 'unknown';
        const smsStatus = customer.sms_subscription_status || 'unknown';
        
        emailSubscriptions[emailStatus] = (emailSubscriptions[emailStatus] || 0) + 1;
        smsSubscriptions[smsStatus] = (smsSubscriptions[smsStatus] || 0) + 1;
      });
    }

    // Calculate customer value metrics
    const { data: valueData, error: valueError } = await supabase
      .from('customers')
      .select('total_spend, transaction_count')
      .not('total_spend', 'is', null);

    if (valueError) {
      console.error('Error fetching value data:', valueError);
    }

    let totalRevenue = 0;
    let totalTransactions = 0;
    let averageOrderValue = 0;
    
    if (valueData && valueData.length > 0) {
      totalRevenue = valueData.reduce((sum, customer) => sum + (customer.total_spend || 0), 0);
      totalTransactions = valueData.reduce((sum, customer) => sum + (customer.transaction_count || 0), 0);
      averageOrderValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;
    }

    return res.status(200).json({
      // Basic counts
      totalCustomers: totalCustomers || 0,
      squareCustomers: squareCustomers || 0,
      marketingOptIns: marketingOptIns || 0,
      recentImports: recentImports || 0,
      
      // Acquisition breakdown
      acquisitionSources: acquisitionBreakdown,
      
      // Growth data
      monthlyGrowth,
      
      // Subscription breakdown
      emailSubscriptions,
      smsSubscriptions,
      
      // Value metrics
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      totalTransactions,
      averageOrderValue: Math.round(averageOrderValue * 100) / 100,
      
      // Calculated metrics
      squareIntegrationRate: totalCustomers > 0 ? Math.round((squareCustomers / totalCustomers) * 100) : 0,
      marketingOptInRate: totalCustomers > 0 ? Math.round((marketingOptIns / totalCustomers) * 100) : 0,
      
      // Metadata
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('Customer stats error:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch customer statistics', 
      details: error.message 
    });
  }
}
