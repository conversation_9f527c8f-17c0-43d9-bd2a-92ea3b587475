import { IncomingForm } from 'formidable';
import fs from 'fs';
import csv from 'csv-parser';
import { v4 as uuidv4 } from 'uuid';
import supabase, { getCurrentUserFromRequest } from '@/lib/supabase';

// Disable body parser for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * Enhanced Customer Import API Endpoint
 * Handles CSV file uploads with validation, duplicate detection, and progress tracking
 * Specifically designed for Google Contacts import data with Square Customer IDs
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check authentication and admin permissions
    const { user, role } = await getCurrentUserFromRequest(req);
    const allowedRoles = ['dev', 'admin'];
    if (!user || !allowedRoles.includes(role)) {
      return res.status(401).json({ error: 'Unauthorized. Admin access required.' });
    }

    // Parse the uploaded file
    const form = new IncomingForm({
      uploadDir: '/tmp',
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
    });

    const { fields, files } = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        else resolve({ fields, files });
      });
    });

    const uploadedFile = files.file;
    if (!uploadedFile) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Validate file type
    const fileName = uploadedFile.originalFilename || uploadedFile.name;
    if (!fileName.toLowerCase().endsWith('.csv')) {
      return res.status(400).json({ error: 'Only CSV files are supported' });
    }

    // Generate import batch ID
    const importBatchId = uuidv4();
    
    // Initialize import log
    const { data: importLog, error: logError } = await supabase
      .from('customer_import_logs')
      .insert([{
        import_batch_id: importBatchId,
        filename: fileName,
        import_status: 'in_progress',
        imported_by: user.id,
        started_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (logError) {
      console.error('Error creating import log:', logError);
      return res.status(500).json({ error: 'Failed to initialize import log' });
    }

    // Process CSV file
    const results = await processCSVFile(uploadedFile.filepath, importBatchId, user.id);
    
    // Update import log with results
    await supabase
      .from('customer_import_logs')
      .update({
        total_records: results.totalRecords,
        successful_imports: results.successfulImports,
        failed_imports: results.failedImports,
        duplicate_emails: results.duplicateEmails,
        import_status: results.failedImports > 0 ? 'completed_with_errors' : 'completed',
        completed_at: new Date().toISOString(),
        error_details: results.errors.length > 0 ? results.errors : null
      })
      .eq('import_batch_id', importBatchId);

    // Clean up uploaded file
    try {
      fs.unlinkSync(uploadedFile.filepath);
    } catch (cleanupError) {
      console.warn('Failed to clean up uploaded file:', cleanupError);
    }

    return res.status(200).json({
      success: true,
      importBatchId,
      results: {
        totalRecords: results.totalRecords,
        successfulImports: results.successfulImports,
        failedImports: results.failedImports,
        duplicateEmails: results.duplicateEmails,
        errors: results.errors
      }
    });

  } catch (error) {
    console.error('Import error:', error);
    return res.status(500).json({ 
      error: 'Import failed', 
      details: error.message 
    });
  }
}

/**
 * Process CSV file and import customers
 */
async function processCSVFile(filePath, importBatchId, userId) {
  return new Promise((resolve, reject) => {
    const results = {
      totalRecords: 0,
      successfulImports: 0,
      failedImports: 0,
      duplicateEmails: 0,
      errors: []
    };

    const customers = [];
    let rowNumber = 0;

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        rowNumber++;
        results.totalRecords++;
        
        try {
          // Validate and clean row data
          const customerData = validateAndCleanCustomerData(row, rowNumber);
          if (customerData.isValid) {
            customers.push({
              ...customerData.data,
              rowNumber,
              importBatchId
            });
          } else {
            results.failedImports++;
            results.errors.push({
              rowNumber,
              error: customerData.error,
              data: row
            });
          }
        } catch (error) {
          results.failedImports++;
          results.errors.push({
            rowNumber,
            error: `Row processing error: ${error.message}`,
            data: row
          });
        }
      })
      .on('end', async () => {
        try {
          // Import customers in batches
          const batchSize = 50;
          for (let i = 0; i < customers.length; i += batchSize) {
            const batch = customers.slice(i, i + batchSize);
            const batchResults = await importCustomerBatch(batch, importBatchId);
            
            results.successfulImports += batchResults.successful;
            results.duplicateEmails += batchResults.duplicates;
            results.failedImports += batchResults.failed;
            results.errors.push(...batchResults.errors);
          }
          
          resolve(results);
        } catch (error) {
          reject(error);
        }
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

/**
 * Validate and clean customer data from CSV row
 */
function validateAndCleanCustomerData(row, rowNumber) {
  try {
    // Required fields validation
    const name = (row.name || '').trim();
    const email = (row.email || '').trim().toLowerCase();
    
    if (!name) {
      return { isValid: false, error: 'Name is required' };
    }
    
    if (!email) {
      return { isValid: false, error: 'Email is required' };
    }
    
    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { isValid: false, error: 'Invalid email format' };
    }

    // Clean and format data
    const customerData = {
      name,
      email,
      phone: cleanPhone(row.phone),
      address: (row.address || '').trim() || null,
      city: (row.city || '').trim() || null,
      state: (row.state || '').trim() || null,
      postal_code: (row.postal_code || '').trim() || null,
      country: (row.country || 'Australia').trim(),
      notes: (row.notes || '').trim() || null,
      marketing_consent: parseBoolean(row.marketing_consent),
      square_customer_id: (row.square_customer_id || '').trim() || null,
      acquisition_source: (row.acquisition_source || '').trim() || null,
      transaction_count: parseNumber(row.transaction_count) || 0,
      total_spend: parseNumber(row.total_spend) || 0.00,
      first_visit: parseDate(row.first_visit),
      last_square_visit: parseDate(row.last_square_visit),
      email_subscription_status: (row.email_subscription_status || 'unknown').trim(),
      sms_subscription_status: (row.sms_subscription_status || 'unknown').trim(),
      customer_memo: (row.customer_memo || '').trim() || null,
      import_source: 'google_contacts_csv'
    };

    return { isValid: true, data: customerData };
  } catch (error) {
    return { isValid: false, error: `Data validation error: ${error.message}` };
  }
}

/**
 * Clean and standardize phone number
 */
function cleanPhone(phone) {
  if (!phone) return null;
  
  // Remove all non-digit characters except +
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // Handle Australian phone formats
  if (cleaned.startsWith('+61')) {
    return cleaned;
  } else if (cleaned.startsWith('61') && cleaned.length === 11) {
    return '+' + cleaned;
  } else if (cleaned.startsWith('04') && cleaned.length === 10) {
    return '+61' + cleaned.substring(1);
  } else if (cleaned.startsWith('0') && cleaned.length === 10) {
    return '+61' + cleaned.substring(1);
  }
  
  return phone; // Return original if can't parse
}

/**
 * Parse boolean values from CSV
 */
function parseBoolean(value) {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    const lower = value.toLowerCase().trim();
    return lower === 'true' || lower === '1' || lower === 'yes' || lower === 'subscribed';
  }
  return false;
}

/**
 * Parse number values from CSV
 */
function parseNumber(value) {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    const cleaned = value.replace(/[^\d.-]/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? null : parsed;
  }
  return null;
}

/**
 * Parse date values from CSV
 */
function parseDate(value) {
  if (!value) return null;
  
  try {
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date.toISOString();
  } catch (error) {
    return null;
  }
}

/**
 * Import a batch of customers with duplicate detection
 */
async function importCustomerBatch(customers, importBatchId) {
  const results = {
    successful: 0,
    duplicates: 0,
    failed: 0,
    errors: []
  };

  for (const customer of customers) {
    try {
      // Check for existing customer by email
      const { data: existingCustomer } = await supabase
        .from('customers')
        .select('id, email')
        .ilike('email', customer.email)
        .single();

      if (existingCustomer) {
        results.duplicates++;
        
        // Log duplicate error
        await supabase
          .from('customer_import_errors')
          .insert([{
            import_batch_id: importBatchId,
            row_number: customer.rowNumber,
            customer_email: customer.email,
            customer_name: customer.name,
            error_type: 'duplicate_email',
            error_message: `Customer with email ${customer.email} already exists`,
            raw_data: customer
          }]);
        
        continue;
      }

      // Insert new customer
      const { data, error } = await supabase
        .from('customers')
        .insert([{
          ...customer,
          import_date: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      results.successful++;
    } catch (error) {
      results.failed++;
      results.errors.push({
        rowNumber: customer.rowNumber,
        error: error.message,
        data: customer
      });

      // Log import error
      await supabase
        .from('customer_import_errors')
        .insert([{
          import_batch_id: importBatchId,
          row_number: customer.rowNumber,
          customer_email: customer.email,
          customer_name: customer.name,
          error_type: 'import_error',
          error_message: error.message,
          raw_data: customer
        }]);
    }
  }

  return results;
}
